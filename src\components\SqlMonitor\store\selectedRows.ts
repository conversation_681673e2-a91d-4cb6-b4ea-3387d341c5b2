import { create } from "zustand";
import type { AlertSend } from "../types";

// 定义选中行状态的接口
interface SelectedRowsState {
  // 选中的行数据
  selectedRows: AlertSend[];

  // 设置选中的行
  setSelectedRows: (rows: AlertSend[]) => void;
  // 添加选中行
  addSelectedRow: (row: AlertSend) => void;
  // 移除选中行
  removeSelectedRow: (rowId: number) => void;
  // 清空选中
  clearSelection: () => void;

  // 批量设置选中状态
  setSelection: (rows: AlertSend[]) => void;
}

export const useSelectedRowsInDrawerStore = create<SelectedRowsState>(
  (set) => ({
    selectedRows: [],

    // 设置选中的行数据
    setSelectedRows: (rows: AlertSend[]) =>
      set({
        selectedRows: rows,
      }),

    // 添加选中行
    addSelectedRow: (row: AlertSend) =>
      set((state) => {
        const isAlreadySelected = state.selectedRows.some(
          (r) => r.id === row.id
        );
        if (isAlreadySelected) return state;

        return {
          selectedRows: [...state.selectedRows, row],
        };
      }),

    // 移除选中行
    removeSelectedRow: (rowId: number) =>
      set((state) => ({
        selectedRows: state.selectedRows.filter((r) => r.id !== rowId),
      })),

    // 清空选中
    clearSelection: () =>
      set({
        selectedRows: [],
      }),

    // 批量设置选中状态
    setSelection: (rows: AlertSend[]) =>
      set({
        selectedRows: rows,
      }),
  })
);
